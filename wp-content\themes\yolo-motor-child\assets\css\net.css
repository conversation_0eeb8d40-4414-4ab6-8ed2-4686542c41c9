/* Cart Page Styling */
#products-table bdi,
#products-table td {
	font-size: 12px;
}

/* Cart wrapper styling */
.woocommerce-cart-wrapper {
	margin: 20px 0;
}

/* Empty cart message styling */
.wc-empty-cart-message {
	text-align: center;
	padding: 40px 20px;
	background-color: #f9f9f9;
	border: 1px solid #ddd;
	border-radius: 4px;
	margin: 20px 0;
}

.wc-empty-cart-message p {
	margin-bottom: 20px;
	font-size: 16px;
	color: #666;
}

.wc-empty-cart-message .button {
	display: inline-block;
	padding: 12px 24px;
	background-color: var(--primary_color, #007cba);
	color: white;
	text-decoration: none;
	border-radius: 4px;
	transition: background-color 0.3s ease;
}

.wc-empty-cart-message .button:hover {
	background-color: var(--secondary_color, #005a87);
}

/* Cart form styling */
.woocommerce-cart-form {
	margin-bottom: 30px;
}

/* Responsive table improvements */
@media (max-width: 768px) {
	.shop_table_responsive {
		font-size: 14px;
	}

	.shop_table_responsive .product-thumbnail img {
		max-width: 60px;
		height: auto;
	}

	.shop_table_responsive .product-name {
		font-size: 14px;
		line-height: 1.4;
	}

	.shop_table_responsive .product-price,
	.shop_table_responsive .product-subtotal {
		font-weight: bold;
	}
}