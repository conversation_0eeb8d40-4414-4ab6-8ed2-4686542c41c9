jQuery(document).ready(function($) {
    // Populate the custom field dropdowns
    for (const [key, values] of Object.entries(customFilterData)) {
        // console.log(key);        
        // console.log(values);
        $.each(values, function(index, value) {
            $(`#${key}`).append(new Option(value, value));
        })
    }

    $('.filter_meta_options').on('click', function(){
    });
    // Initialize DataTable
    var table = $('#products-table').DataTable({
        serverSide: true,
        ajax: {
            url: ajax_object.ajax_url,
            type: 'POST',
            data: function(d) {
                d.action = 'fetch_products';
                d.search.value = $('#custom-search').val();
                d.customFields = {
                    brand: $('#brand').val(), 
                    product_line: $('#product_line').val(),
                    product_family: $('#product_family').val(),
                    model_size: $('#model_size').val(),
                    ce_approved: $('#ce_approved').val(),
                };
                d.inStockOnly = $('#in-stock').is(':checked');
                d.sortBy = $('#sort-by').val();
            },
            beforeSend: function() {
                $('#products-table-wrap').addClass('bg--loading');
            },
            complete: function() {
                $('#products-table-wrap').removeClass('bg--loading');
            }
        },
        columns: [
            { data: 'quantity', orderable: false, width: '110px' },
            { data: 'title' },
            { data: 'stock' },
            { data: 'price' },
            {
                data: 'net_price',
                orderable: false,
                defaultContent: '<div class="sap-net-price-loading">Loading...</div>'
            },
            { data: 'brand' },
            { data: 'product_line' },
            { data: 'product_family' },
            { data: 'model_size' },
            { data: 'ce_approver' }
        ],
        paging: true,
        pageLength: 10,
        searching: false,
        language: {
            search: "Filter products:",
            lengthMenu: "Show _MENU_ products",
            info: "<button id='add-selected-to-cart' class='orange-bg white w100 no-border f-16 py-1 mr-2 px-2 add_all_to_cart_button normal' type='button'>Add To Cart</button>Showing _START_-_END_ of _TOTAL_",
            paginate: {
                first: "<<",
                last: ">>",
                next: ">",
                previous: "<"
            }
        },
        ordering: true,
        responsive: true,
        lengthChange: true,  // Enable "per page" dropdown
        dom: '<"top"lf>rt<"bottom"ip><"clear">',
        // lengthChange: false,  // Hide "per page" dropdown
        // dom: 'rtip',  // Only show table, pagination, and information, remove length menu
    });
    table.on('draw', function() {
        var info = table.page.info();
        const recordsTotalFormated = info.recordsTotal.toLocaleString('en-US');
        var infoHtml = `SHOWING ${info.start + 1} TO ${info.end} of ${recordsTotalFormated}`;

        // Update the custom location next to the "Sort by" dropdown
        $('#pagination-info').html(infoHtml);

        // Fetch SAP pricing for all visible products (with small delay to ensure DOM is ready)
        setTimeout(function() {
            fetchSapPricingForTable();
        }, 100);
    });
    // Event listener for the "Apply All" button
    $('#apply-filters').on('click', function() {
        table.ajax.reload();
    });

    // Function to fetch SAP pricing for all visible products in the table
    function fetchSapPricingForTable() {
        console.log('fetchSapPricingForTable called');

        // Collect all SKUs from the current page
        var skus = [];
        var totalRows = $('#products-table tbody tr').length;

        console.log('Total visible rows found:', totalRows);

        // Look for SKUs in the table rows - they appear in the "Materials #" column
        var rowsWithSku = 0;
        var rowsWithoutSku = 0;

        $('#products-table tbody tr').each(function(index) {
            var $row = $(this);
            // The SKU appears to be in the second column (Materials #)
            var $titleCell = $row.find('td:nth-child(2)');
            var titleText = $titleCell.text();

            // Extract SKU from the title text (it appears to be the first line)
            var lines = titleText.trim().split('\n');
            var sku = '';

            // Look for a line that looks like a SKU (numbers or alphanumeric)
            for (var i = 0; i < lines.length; i++) {
                var line = lines[i].trim();
                // Match numeric SKUs (like 100065) or alphanumeric SKUs (like C2514CBT, C158C)
                if (line.match(/^[A-Z0-9]+$/i) && line.length >= 3) {
                    sku = line;
                    break;
                }
            }

            if (sku) {
                skus.push(sku);
                rowsWithSku++;
                // Add the SKU as a data attribute to the net price cell for later reference
                $row.find('td:nth-child(5)').attr('data-sku', sku);
            } else {
                rowsWithoutSku++;
                // Debug: log first few rows that don't have valid SKUs
                if (rowsWithoutSku <= 5) {
                    console.log('Row', index + 1, 'no SKU found.');
                    console.log('  Title text length:', titleText.length);
                    console.log('  Title text:', JSON.stringify(titleText));
                    console.log('  Lines:', lines);
                    console.log('  HTML content:', $titleCell.html());
                }
            }
        });

        console.log('Rows with SKU:', rowsWithSku, 'Rows without SKU:', rowsWithoutSku);

        if (skus.length === 0) {
            console.log('No SKUs found in table');
            return;
        }

        console.log('Fetching SAP pricing for SKUs:', skus);

        // Add debug info to page
        updateDebugInfo('REQUEST', {
            action: 'get_sap_pricing_bulk',
            skus: skus,
            ajax_url: ajax_object.ajax_url
        });

        // Make AJAX call to get SAP pricing for all SKUs
        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            dataType: 'text', // Get raw text to handle JSON parsing manually
            data: {
                action: 'get_sap_pricing_bulk',
                skus: skus
            },
            success: function(responseText) {
                console.log('SAP pricing raw response:', responseText);

                // Clean the response - remove any HTML that might be prepended
                var jsonStart = responseText.indexOf('{');
                if (jsonStart > 0) {
                    responseText = responseText.substring(jsonStart);
                    console.log('Cleaned response:', responseText);
                }

                // Parse JSON manually
                var response;
                try {
                    response = JSON.parse(responseText);
                } catch (e) {
                    console.error('JSON parse error:', e);
                    updateDebugInfo('PARSE_ERROR', {error: e.message, responseText: responseText.substring(0, 500)});
                    $('#products-table tbody tr td:nth-child(5)').html('<span style="color: #666;">Parse Error</span>');
                    return;
                }

                console.log('SAP pricing parsed response:', response);
                updateDebugInfo('RESPONSE', response);

                if (response.success && response.data && response.data.pricing) {
                    // Update each product's net price using the data-sku attribute
                    $('#products-table tbody tr').each(function() {
                        var $row = $(this);
                        var $netPriceCell = $row.find('td:nth-child(5)');
                        var sku = $netPriceCell.data('sku');

                        if (sku && response.data.pricing[sku]) {
                            var sapData = response.data.pricing[sku];
                            var netPriceHtml = '<div class="sap-table-pricing">';

                            if (sapData.formatted_net_value) {
                                netPriceHtml += '<span class="sap-net-price" style="font-weight: 600; color: #007cba;">' +
                                               sapData.formatted_net_value + '</span>';
                            } else {
                                netPriceHtml += '<span style="color: #666;">N/A</span>';
                            }

                            netPriceHtml += '</div>';
                            $netPriceCell.html(netPriceHtml);
                        } else {
                            $netPriceCell.html('<span style="color: #666;">N/A</span>');
                        }
                    });
                } else {
                    // Handle error - show N/A for all
                    $('#products-table tbody tr td:nth-child(5)').html('<span style="color: #666;">N/A</span>');
                }
            },
            error: function(xhr, status, error) {
                console.error('Failed to fetch SAP pricing:', error);
                updateDebugInfo('ERROR', {
                    status: status,
                    error: error,
                    responseText: xhr.responseText
                });
                $('#products-table tbody tr td:nth-child(5)').html('<span style="color: #666;">Error</span>');
            }
        });
    }

    // Function to update debug information
    function updateDebugInfo(type, data) {
        // Create debug container if it doesn't exist
        if ($('#sap-debug-info').length === 0) {
            $('#products-table-wrap').after('<div id="sap-debug-info" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 5px;"><h4>SAP Pricing Debug Info</h4></div>');
        }

        var timestamp = new Date().toLocaleTimeString();
        var debugHtml = '<div style="margin: 10px 0; padding: 10px; background: ' +
                       (type === 'ERROR' ? '#ffebee' : type === 'REQUEST' ? '#e3f2fd' : '#e8f5e8') +
                       '; border-left: 4px solid ' +
                       (type === 'ERROR' ? '#f44336' : type === 'REQUEST' ? '#2196f3' : '#4caf50') +
                       ';">';
        debugHtml += '<strong>' + type + ' (' + timestamp + '):</strong><br>';
        debugHtml += '<pre style="margin: 5px 0; font-size: 12px; white-space: pre-wrap;">' + JSON.stringify(data, null, 2) + '</pre>';
        debugHtml += '</div>';

        $('#sap-debug-info').append(debugHtml);

        // Keep only last 5 debug entries
        var debugEntries = $('#sap-debug-info > div');
        if (debugEntries.length > 6) { // 6 because we have the h4 title
            debugEntries.first().next().remove();
        }
    }
});
