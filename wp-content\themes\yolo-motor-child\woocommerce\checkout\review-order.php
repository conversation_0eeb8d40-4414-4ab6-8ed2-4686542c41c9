<?php
/**
 * Review order table
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/review-order.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 5.2.0
 */

defined( 'ABSPATH' ) || exit;
?>
<table class="shop_table woocommerce-checkout-review-order-table">
	<thead>
        <tr>
            <th class="product-name">Items (<?php echo WC()->cart->get_cart_contents_count(); ?>)</th>
            <th class="product-quantity">QTY</th>
            <th class="product-total"></th>
        </tr>
	</thead>
	<tbody>
		<?php
		do_action( 'woocommerce_review_order_before_cart_contents' );

        foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
            $_product   = $cart_item['data'];
            if ( $_product && $_product->exists() && $cart_item['quantity'] > 0 ) {
                ?>
                <tr class="<?php echo esc_attr( apply_filters( 'woocommerce_cart_item_class', 'cart_item', $cart_item, $cart_item_key ) ); ?>">
                    
                    <!-- First Column: Image + Product Name -->
                    <td class="product-name">
                        <?php
                        $thumbnail = apply_filters( 'woocommerce_cart_item_thumbnail', $_product->get_image(), $cart_item, $cart_item_key );
                        echo $thumbnail; // Display product image
                        echo '<span class="product-title">' . $_product->get_name() . '</span>'; // Display product name
                        ?>
                    </td>

                    <!-- Second Column: Quantity -->
                    <td class="product-quantity">
                        <?php echo apply_filters( 'woocommerce_checkout_cart_item_quantity', ' <strong class="product-quantity">' . sprintf( '&times; %s', $cart_item['quantity'] ) . '</strong>', $cart_item, $cart_item_key ); ?>
                    </td>

                    <!-- Third Column: Subtotal -->
                    <td class="product-total">
                        <?php
                        // Show SAP pricing for logged-in users
                        if ( is_user_logged_in() && function_exists('hytec_sap_get_or_fetch_pricing_with_quantity') ) {
                            $user_id = get_current_user_id();
                            $quantity = isset($cart_item['quantity']) ? max(1, intval($cart_item['quantity'])) : 1;

                            // Get SAP pricing for this product with actual cart quantity
                            $pricing = hytec_sap_get_or_fetch_pricing_with_quantity( $_product, $user_id, $quantity );

                            if ( is_array( $pricing ) && isset( $pricing['net_value'] ) && $pricing['net_value'] !== '' ) {
                                // Convert SAP response (divide by 100 for proper decimal format)
                                $net_value_decimal = (float)$pricing['net_value'] / 100;

                                // Format currency based on user's company code
                                $company_code = get_user_meta($user_id, '_companycode', true);
                                if ($company_code === '3090') {
                                    // US customers - show in USD
                                    echo '<span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">$</span>' . number_format($net_value_decimal, 2, '.', '') . '</bdi></span>';
                                } else {
                                    // Non-US customers - show in EUR
                                    echo '<span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">€</span>' . number_format($net_value_decimal, 2, '.', '') . '</bdi></span>';
                                }
                            } else {
                                // Fallback to WooCommerce subtotal if SAP not available
                                echo WC()->cart->get_product_subtotal( $_product, $cart_item['quantity'] );
                            }
                        } else {
                            // Fallback for non-logged-in users
                            echo WC()->cart->get_product_subtotal( $_product, $cart_item['quantity'] );
                        }
                        ?>
                    </td>

                </tr>
                <?php
            }
        }

		do_action( 'woocommerce_review_order_after_cart_contents' );
		?>
	</tbody>
	<tfoot>

		<tr class="cart-subtotal">
			<th><?php esc_html_e( 'Subtotal', 'woocommerce' ); ?></th>
			<td><?php wc_cart_totals_subtotal_html(); ?></td>
		</tr>

		<?php foreach ( WC()->cart->get_coupons() as $code => $coupon ) : ?>
			<tr class="cart-discount coupon-<?php echo esc_attr( sanitize_title( $code ) ); ?>">
				<th><?php wc_cart_totals_coupon_label( $coupon ); ?></th>
				<td><?php wc_cart_totals_coupon_html( $coupon ); ?></td>
			</tr>
		<?php endforeach; ?>

		<?php if ( WC()->cart->needs_shipping() && WC()->cart->show_shipping() ) : ?>

			<?php do_action( 'woocommerce_review_order_before_shipping' ); ?>

			<?php wc_cart_totals_shipping_html(); ?>

			<?php do_action( 'woocommerce_review_order_after_shipping' ); ?>

		<?php endif; ?>

		<?php foreach ( WC()->cart->get_fees() as $fee ) : ?>
			<tr class="fee">
				<th><?php echo esc_html( $fee->name ); ?></th>
				<td><?php wc_cart_totals_fee_html( $fee ); ?></td>
			</tr>
		<?php endforeach; ?>

		<?php if ( wc_tax_enabled() && ! WC()->cart->display_prices_including_tax() ) : ?>
			<?php if ( 'itemized' === get_option( 'woocommerce_tax_total_display' ) ) : ?>
				<?php foreach ( WC()->cart->get_tax_totals() as $code => $tax ) : // phpcs:ignore WordPress.WP.GlobalVariablesOverride.Prohibited ?>
					<tr class="tax-rate tax-rate-<?php echo esc_attr( sanitize_title( $code ) ); ?>">
						<th><?php echo esc_html( $tax->label ); ?></th>
						<td><?php echo wp_kses_post( $tax->formatted_amount ); ?></td>
					</tr>
				<?php endforeach; ?>
			<?php else : ?>
				<tr class="tax-total">
					<th><?php echo esc_html( WC()->countries->tax_or_vat() ); ?></th>
					<td><?php wc_cart_totals_taxes_total_html(); ?></td>
				</tr>
			<?php endif; ?>
		<?php endif; ?>

		<?php do_action( 'woocommerce_review_order_before_order_total' ); ?>

		<tr class="order-total">
			<th><?php esc_html_e( 'Total', 'woocommerce' ); ?></th>
			<th>&nbsp;</th>
			<td>
				<?php
				// Calculate SAP total for logged-in users
				if ( is_user_logged_in() && function_exists('hytec_sap_get_or_fetch_pricing_with_quantity') ) {
					$sap_total = 0;
					$user_id = get_current_user_id();

					// Calculate total based on SAP pricing for each cart item
					foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
						$product = $cart_item['data'];
						if ( ! $product || ! ($product instanceof WC_Product) ) {
							continue;
						}

						$quantity = isset($cart_item['quantity']) ? max(1, intval($cart_item['quantity'])) : 1;

						// Get SAP pricing for this product with actual cart quantity
						$pricing = hytec_sap_get_or_fetch_pricing_with_quantity( $product, $user_id, $quantity );

						if ( is_array( $pricing ) && isset( $pricing['net_value'] ) && $pricing['net_value'] !== '' ) {
							// Convert SAP response (divide by 100) and add to total
							$net_value_decimal = (float)$pricing['net_value'] / 100;
							$sap_total += $net_value_decimal;
						} else {
							// Fallback to WooCommerce price if SAP not available
							$fallback_price = $product->get_price() * $quantity;
							$sap_total += $fallback_price;
						}
					}

					// Format the SAP total with proper currency
					$company_code = get_user_meta($user_id, '_companycode', true);
					if ($company_code === '3090') {
						// US customers - show in USD
						echo '<span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">$</span>' . number_format($sap_total, 2, '.', '') . '</bdi></span>';
					} else {
						// Non-US customers - show in EUR
						echo '<span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">€</span>' . number_format($sap_total, 2, '.', '') . '</bdi></span>';
					}
				} else {
					// Fallback for non-logged-in users
					wc_cart_totals_order_total_html();
				}
				?>
			</td>
		</tr>

		<?php do_action( 'woocommerce_review_order_after_order_total' ); ?>

	</tfoot>
</table>
