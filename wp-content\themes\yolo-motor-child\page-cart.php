<?php
/**
 * Custom Cart Page Template (No WooCommerce Blocks)
 * Template Name: Custom Cart
 */

// Define missing function to prevent errors
if ( ! function_exists( 'yolo_include_footer_id' ) ) {
    function yolo_include_footer_id() {
        return '';
    }
}

get_header(); ?>

<div class="container">
    <div class="row">
        <div class="col-md-12">
            <h1 class="page-title">Shopping Cart</h1>
            
            <?php
            // Check if WooCommerce is active and cart has items
            if ( class_exists( 'WooCommerce' ) ) {
                
                if ( WC()->cart->is_empty() ) {
                    ?>
                    <div class="wc-empty-cart-message">
                        <p><?php esc_html_e( 'Your cart is currently empty.', 'woocommerce' ); ?></p>
                        <p>
                            <a class="button wc-backward" href="<?php echo esc_url( apply_filters( 'woocommerce_return_to_shop_redirect', wc_get_page_permalink( 'shop' ) ) ); ?>">
                                <?php esc_html_e( 'Return to shop', 'woocommerce' ); ?>
                            </a>
                        </p>
                    </div>
                    <?php
                } else {
                    ?>
                    <form class="woocommerce-cart-form" action="<?php echo esc_url( wc_get_cart_url() ); ?>" method="post">
                        <?php do_action( 'woocommerce_before_cart_table' ); ?>

                        <table class="shop_table shop_table_responsive cart woocommerce-cart-form__contents" cellspacing="0">
                            <thead>
                                <tr>
                                    <th class="product-remove">&nbsp;</th>
                                    <th class="product-thumbnail">&nbsp;</th>
                                    <th class="product-name"><?php esc_html_e( 'Product', 'woocommerce' ); ?></th>
                                    <th class="product-price"><?php esc_html_e( 'Price', 'woocommerce' ); ?></th>
                                    <th class="product-quantity"><?php esc_html_e( 'Quantity', 'woocommerce' ); ?></th>
                                    <th class="product-subtotal"><?php esc_html_e( 'Subtotal', 'woocommerce' ); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php do_action( 'woocommerce_before_cart_contents' ); ?>

                                <?php
                                foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
                                    $_product   = apply_filters( 'woocommerce_cart_item_product', $cart_item['data'], $cart_item, $cart_item_key );
                                    $product_id = apply_filters( 'woocommerce_cart_item_product_id', $cart_item['product_id'], $cart_item, $cart_item_key );

                                    if ( $_product && $_product->exists() && $cart_item['quantity'] > 0 && apply_filters( 'woocommerce_cart_item_visible', true, $cart_item, $cart_item_key ) ) {
                                        $product_permalink = apply_filters( 'woocommerce_cart_item_permalink', $_product->is_visible() ? $_product->get_permalink( $cart_item ) : '', $cart_item, $cart_item_key );
                                        ?>
                                        <tr class="woocommerce-cart-form__cart-item <?php echo esc_attr( apply_filters( 'woocommerce_cart_item_class', 'cart_item', $cart_item, $cart_item_key ) ); ?>">

                                            <td class="product-remove">
                                                <?php
                                                echo apply_filters( // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
                                                    'woocommerce_cart_item_remove_link',
                                                    sprintf(
                                                        '<a href="%s" class="remove" aria-label="%s" data-product_id="%s" data-product_sku="%s">&times;</a>',
                                                        esc_url( wc_get_cart_remove_url( $cart_item_key ) ),
                                                        esc_html__( 'Remove this item', 'woocommerce' ),
                                                        esc_attr( $product_id ),
                                                        esc_attr( $_product->get_sku() )
                                                    ),
                                                    $cart_item_key
                                                );
                                                ?>
                                            </td>

                                            <td class="product-thumbnail">
                                                <?php
                                                $thumbnail = apply_filters( 'woocommerce_cart_item_thumbnail', $_product->get_image(), $cart_item, $cart_item_key );

                                                if ( ! $product_permalink ) {
                                                    echo $thumbnail; // PHPCS: XSS ok.
                                                } else {
                                                    printf( '<a href="%s">%s</a>', esc_url( $product_permalink ), $thumbnail ); // PHPCS: XSS ok.
                                                }
                                                ?>
                                            </td>

                                            <td class="product-name" data-title="<?php esc_attr_e( 'Product', 'woocommerce' ); ?>">
                                                <?php
                                                if ( ! $product_permalink ) {
                                                    echo wp_kses_post( apply_filters( 'woocommerce_cart_item_name', $_product->get_name(), $cart_item, $cart_item_key ) . '&nbsp;' );
                                                } else {
                                                    echo wp_kses_post( apply_filters( 'woocommerce_cart_item_name', sprintf( '<a href="%s">%s</a>', esc_url( $product_permalink ), $_product->get_name() ), $cart_item, $cart_item_key ) );
                                                }

                                                do_action( 'woocommerce_after_cart_item_name', $cart_item, $cart_item_key );

                                                // Meta data
                                                echo wc_get_formatted_cart_item_data( $cart_item ); // PHPCS: XSS ok.

                                                // Backorder notification
                                                if ( $_product->backorders_require_notification() && $_product->is_on_backorder( $cart_item['quantity'] ) ) {
                                                    echo wp_kses_post( apply_filters( 'woocommerce_cart_item_backorder_notification', '<p class="backorder_notification">' . esc_html__( 'Available on backorder', 'woocommerce' ) . '</p>', $product_id ) );
                                                }
                                                ?>
                                            </td>

                                            <td class="product-price" data-title="<?php esc_attr_e( 'Price', 'woocommerce' ); ?>">
                                                <?php
                                                // Check for custom SAP pricing
                                                if ( is_user_logged_in() && function_exists('hytec_sap_get_or_fetch_pricing_with_quantity') ) {
                                                    $user_id = get_current_user_id();
                                                    $sap_pricing = hytec_sap_get_or_fetch_pricing_with_quantity( $_product, $user_id, 1 );

                                                    if ( is_array( $sap_pricing ) && isset( $sap_pricing['net_value'] ) && $sap_pricing['net_value'] !== '' ) {
                                                        $net_value_decimal = (float)$sap_pricing['net_value'] / 100;
                                                        $company_code = get_user_meta($user_id, '_companycode', true);

                                                        if ($company_code === '3090') {
                                                            echo '$' . number_format($net_value_decimal, 2, '.', '');
                                                        } else {
                                                            echo '€' . number_format($net_value_decimal, 2, '.', '');
                                                        }
                                                    } else {
                                                        echo apply_filters( 'woocommerce_cart_item_price', WC()->cart->get_product_price( $_product ), $cart_item, $cart_item_key );
                                                    }
                                                } else {
                                                    echo apply_filters( 'woocommerce_cart_item_price', WC()->cart->get_product_price( $_product ), $cart_item, $cart_item_key );
                                                }
                                                ?>
                                            </td>

                                            <td class="product-quantity" data-title="<?php esc_attr_e( 'Quantity', 'woocommerce' ); ?>">
                                                <?php
                                                if ( $_product->is_sold_individually() ) {
                                                    $min_quantity = 1;
                                                    $max_quantity = 1;
                                                } else {
                                                    $min_quantity = 0;
                                                    $max_quantity = $_product->get_max_purchase_quantity();
                                                }

                                                $product_quantity = woocommerce_quantity_input(
                                                    array(
                                                        'input_name'   => "cart[{$cart_item_key}][qty]",
                                                        'input_value'  => $cart_item['quantity'],
                                                        'max_value'    => $max_quantity,
                                                        'min_value'    => $min_quantity,
                                                        'product_name' => $_product->get_name(),
                                                    ),
                                                    $_product,
                                                    false
                                                );

                                                echo apply_filters( 'woocommerce_cart_item_quantity', $product_quantity, $cart_item_key, $cart_item ); // PHPCS: XSS ok.
                                                ?>
                                            </td>

                                            <td class="product-subtotal" data-title="<?php esc_attr_e( 'Subtotal', 'woocommerce' ); ?>">
                                                <?php
                                                // Check for custom SAP pricing for subtotal
                                                if ( is_user_logged_in() && function_exists('hytec_sap_get_or_fetch_pricing_with_quantity') ) {
                                                    $user_id = get_current_user_id();
                                                    $sap_pricing = hytec_sap_get_or_fetch_pricing_with_quantity( $_product, $user_id, $cart_item['quantity'] );

                                                    if ( is_array( $sap_pricing ) && isset( $sap_pricing['net_value'] ) && $sap_pricing['net_value'] !== '' ) {
                                                        $net_value_decimal = (float)$sap_pricing['net_value'] / 100;
                                                        $company_code = get_user_meta($user_id, '_companycode', true);

                                                        if ($company_code === '3090') {
                                                            echo '$' . number_format($net_value_decimal, 2, '.', '');
                                                        } else {
                                                            echo '€' . number_format($net_value_decimal, 2, '.', '');
                                                        }
                                                    } else {
                                                        echo apply_filters( 'woocommerce_cart_item_subtotal', WC()->cart->get_product_subtotal( $_product, $cart_item['quantity'] ), $cart_item, $cart_item_key );
                                                    }
                                                } else {
                                                    echo apply_filters( 'woocommerce_cart_item_subtotal', WC()->cart->get_product_subtotal( $_product, $cart_item['quantity'] ), $cart_item, $cart_item_key );
                                                }
                                                ?>
                                            </td>
                                        </tr>
                                        <?php
                                    }
                                }
                                ?>

                                <?php do_action( 'woocommerce_cart_contents' ); ?>

                                <tr>
                                    <td colspan="6" class="actions">
                                        <?php if ( wc_coupons_enabled() ) { ?>
                                            <div class="coupon">
                                                <label for="coupon_code"><?php esc_html_e( 'Coupon:', 'woocommerce' ); ?></label> 
                                                <input type="text" name="coupon_code" class="input-text" id="coupon_code" value="" placeholder="<?php esc_attr_e( 'Coupon code', 'woocommerce' ); ?>" /> 
                                                <button type="submit" class="button" name="apply_coupon" value="<?php esc_attr_e( 'Apply coupon', 'woocommerce' ); ?>"><?php esc_attr_e( 'Apply coupon', 'woocommerce' ); ?></button>
                                                <?php do_action( 'woocommerce_cart_coupon' ); ?>
                                            </div>
                                        <?php } ?>

                                        <button type="submit" class="button" name="update_cart" value="<?php esc_attr_e( 'Update cart', 'woocommerce' ); ?>"><?php esc_html_e( 'Update cart', 'woocommerce' ); ?></button>

                                        <?php do_action( 'woocommerce_cart_actions' ); ?>

                                        <?php wp_nonce_field( 'woocommerce-cart', 'woocommerce-cart-nonce' ); ?>
                                    </td>
                                </tr>

                                <?php do_action( 'woocommerce_after_cart_contents' ); ?>
                            </tbody>
                        </table>
                        <?php do_action( 'woocommerce_after_cart_table' ); ?>
                    </form>

                    <?php do_action( 'woocommerce_before_cart_collaterals' ); ?>

                    <div class="cart-collaterals">
                        <?php
                        /**
                         * Cart collaterals hook.
                         *
                         * @hooked woocommerce_cross_sell_display
                         * @hooked woocommerce_cart_totals - 10
                         */
                        do_action( 'woocommerce_cart_collaterals' );
                        ?>
                    </div>

                    <?php do_action( 'woocommerce_after_cart' ); ?>
                    <?php
                }
            } else {
                echo '<p>WooCommerce is not active.</p>';
            }
            ?>
        </div>
    </div>
</div>

<?php get_footer(); ?>
